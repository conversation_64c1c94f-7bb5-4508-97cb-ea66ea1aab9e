{"version": "0.2.0", "compounds": [{"name": "Launch Flask and Celery", "configurations": ["Python: Flask", "Python: <PERSON><PERSON><PERSON>"]}], "configurations": [{"name": "Python: Flask", "consoleName": "Flask", "type": "debugpy", "request": "launch", "python": "${workspaceFolder}/.venv/bin/python", "cwd": "${workspaceFolder}/api", "envFile": ".env", "module": "flask", "justMyCode": true, "jinja": true, "env": {"FLASK_APP": "app.py", "GEVENT_SUPPORT": "True"}, "args": ["run", "--port=5001"]}, {"name": "Python: <PERSON><PERSON><PERSON>", "consoleName": "Celery", "type": "debugpy", "request": "launch", "python": "${workspaceFolder}/.venv/bin/python", "cwd": "${workspaceFolder}/api", "module": "celery", "justMyCode": true, "envFile": ".env", "console": "integratedTerminal", "env": {"FLASK_APP": "app.py", "FLASK_DEBUG": "1", "GEVENT_SUPPORT": "True"}, "args": ["-A", "app.celery", "worker", "-P", "gevent", "-c", "1", "--loglevel", "DEBUG", "-Q", "dataset,generation,mail,ops_trace,app_deletion"]}]}