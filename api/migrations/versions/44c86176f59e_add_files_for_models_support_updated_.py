"""add files for Models support updated dify

Revision ID: 44c86176f59e
Revises: 34546a113f9b
Create Date: 2025-06-04 14:44:16.665651

"""
from alembic import op
import models as models
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '44c86176f59e'
down_revision = '34546a113f9b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('datasets', schema=None) as batch_op:
        batch_op.alter_column('retrieval_model',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.Text(),
               existing_nullable=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('datasets', schema=None) as batch_op:
        batch_op.alter_column('retrieval_model',
               existing_type=sa.Text(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)

    # ### end Alembic commands ###
